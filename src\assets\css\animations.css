/* ===== ANIMATION UTILITIES ===== */
/* Define reusable animations and transition classes */

/* Fade In Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Slide Up Animation */
@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Slide Down Animation */
@keyframes slideDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Slide In Right Animation */
@keyframes slideInRight {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Slide In Left Animation */
@keyframes slideInLeft {
  from {
    transform: translateX(-30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Scale Animation */
@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Pulse Animation */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Shake Animation */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* Rotate Animation */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Bounce Animation */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-15px);
  }
  60% {
    transform: translateY(-10px);
  }
}

/* ===== Utility Classes ===== */
/* Animation Classes - FIXED VERSION */
.fade-in {
  opacity: 1; /* Ensure visible by default */
  animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
  opacity: 1; /* Ensure visible by default */
  animation: slideUp 0.6s ease-in-out;
}

.slide-down {
  opacity: 1; /* Ensure visible by default */
  animation: slideDown 0.6s ease-in-out;
}

.slide-in-right {
  opacity: 1; /* Ensure visible by default */
  animation: slideInRight 0.6s ease-in-out;
}

.slide-in-left {
  opacity: 1; /* Ensure visible by default */
  animation: slideInLeft 0.6s ease-in-out;
}

.scale-in {
  opacity: 1; /* Ensure visible by default */
  animation: scaleIn 0.5s ease-in-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.shake {
  animation: shake 0.8s ease-in-out;
}

.rotate {
  animation: rotate 2s linear infinite;
}

.bounce {
  animation: bounce 1.5s infinite;
}

/* Staggered Animation Delays */
.delay-1 {
  animation-delay: 0.1s;
}

.delay-2 {
  animation-delay: 0.2s;
}

.delay-3 {
  animation-delay: 0.3s;
}

.delay-4 {
  animation-delay: 0.4s;
}

.delay-5 {
  animation-delay: 0.5s;
}

/* Transition Utilities */
.transition-all {
  transition: all 0.3s ease-in-out;
}

.transition-transform {
  transition: transform 0.3s ease-in-out;
}

.transition-opacity {
  transition: opacity 0.3s ease-in-out;
}

.transition-colors {
  transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
}

.transition-shadow {
  transition: box-shadow 0.3s ease-in-out;
}

/* Enhanced Hover Effects */
.hover-scale {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-lift {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.hover-shadow {
  transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

/* New hover effects for courses page */
.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(var(--bs-primary-rgb), 0.3);
}

.hover-bounce {
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.hover-bounce:hover {
  transform: scale(1.05);
}

/* ===== Page Transition Effects ===== */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 400ms, transform 400ms;
}

.page-exit {
  opacity: 1;
}

.page-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}

/* ===== Button Animations ===== */
.btn-animated {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-animated::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.btn-animated:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

/* Loading Spinners with Better Animation */
.spinner {
  display: inline-block;
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--bs-primary);
  animation: rotate 1s ease-in-out infinite;
}

.spinner-sm {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.spinner-lg {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

/* Card Hover Effects */
.card.hover-effect {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card.hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Progress Bar Animation */
.animated-progress-bar {
  transition: width 1s ease-in-out;
}

/* Image hover zoom effect */
.img-hover-zoom {
  overflow: hidden;
}

.img-hover-zoom img {
  transition: transform 0.5s ease;
}

.img-hover-zoom:hover img {
  transform: scale(1.1);
}

/* Scroll Reveal Animation Classes - FIXED VERSION */
.reveal {
  opacity: 1; /* Ensure visible by default */
  transform: translateY(0); /* Start in final position */
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.reveal.active {
  opacity: 1;
  transform: translateY(0);
}

.reveal-left {
  opacity: 1; /* Ensure visible by default */
  transform: translateX(0); /* Start in final position */
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.reveal-left.active {
  opacity: 1;
  transform: translateX(0);
}

.reveal-right {
  opacity: 1; /* Ensure visible by default */
  transform: translateX(0); /* Start in final position */
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.reveal-right.active {
  opacity: 1;
  transform: translateX(0);
}

/* =========================================
   ANIMATION LIBRARY FOR E-LEARNING PLATFORM
   ========================================= */

/* Transition utility classes */
.transition-all {
  transition: all 0.3s ease;
}

.transition-transform {
  transition: transform 0.3s ease;
}

.transition-opacity {
  transition: opacity 0.3s ease;
}

.transition-colors {
  transition: color 0.3s ease, background-color 0.3s ease;
}

/* Hover effect utilities */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-primary:hover {
  color: var(--bs-primary) !important;
}

.hover-bg:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.hover-shadow:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Image zoom effect */
.img-hover-zoom {
  overflow: hidden;
}

.img-hover-zoom img {
  transition: transform 0.5s ease;
}

.img-hover-zoom:hover img {
  transform: scale(1.05);
}

/* Button animations */
.btn-animated {
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: all 0.3s ease;
}

.btn-animated::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s ease;
  z-index: -1;
}

.btn-animated:hover::before {
  left: 100%;
}

/* Entrance animations - FIXED VERSION */
.fade-in {
  opacity: 1; /* Ensure visible by default */
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.slide-in-left {
  opacity: 1; /* Ensure visible by default */
  animation: slideInLeft 0.5s ease forwards;
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  opacity: 1; /* Ensure visible by default */
  animation: slideInRight 0.5s ease forwards;
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-up {
  opacity: 1; /* Ensure visible by default */
  animation: slideUp 0.5s ease forwards;
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.scale-in {
  opacity: 1; /* Ensure visible by default */
  animation: scaleIn 0.5s ease forwards;
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Add delay utilities */
.delay-1 {
  animation-delay: 0.2s !important;
}

.delay-2 {
  animation-delay: 0.4s !important;
}

.delay-3 {
  animation-delay: 0.6s !important;
}

/* Scroll reveal animations - FIXED VERSION */
.reveal, .reveal-left, .reveal-right {
  opacity: 1; /* Ensure visible by default */
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.reveal {
  transform: translateY(0); /* Start in final position */
}

.reveal-left {
  transform: translateX(0); /* Start in final position */
}

.reveal-right {
  transform: translateX(0); /* Start in final position */
}

.reveal.active, .reveal-left.active, .reveal-right.active {
  opacity: 1;
  transform: translate(0);
}

/* Translate and fade */
.translate-y-fade {
  opacity: 0;
  transform: translateY(20px);
  animation: translateYFade 0.5s ease forwards;
}

@keyframes translateYFade {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pulse animation for buttons */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--bs-primary-rgb), 0.7);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(var(--bs-primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--bs-primary-rgb), 0);
  }
}

/* Float animation for decorative elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Page transitions */
.page-container {
  position: relative;
  width: 100%;
}

.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 400ms, transform 400ms;
}

.page-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 400ms, transform 400ms;
}

/* Loading pulse animation for course loading state */
.loading-pulse {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
}

.loading-pulse .loading-bar {
  width: 8px;
  height: 30px;
  background-color: var(--bs-primary);
  border-radius: 4px;
  animation: loadingPulse 1.2s infinite ease-in-out;
}

.loading-pulse .loading-bar:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-pulse .loading-bar:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loadingPulse {
  0%, 100% {
    transform: scaleY(0.5);
    opacity: 0.5;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* Enhanced Course Page Animations */
.stagger-animation {
  animation-delay: calc(var(--stagger-delay, 0) * 0.1s);
}

.smooth-entrance {
  animation: smoothEntrance 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes smoothEntrance {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-border {
  position: relative;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, var(--bs-primary), #4f46e5) border-box;
  border: 2px solid transparent;
}

/* Improved button animations */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-enhanced:hover::before {
  left: 100%;
}

.btn-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}