# Ignore node_modules
node_modules/
node_modules/.cache/
node_modules/**/*.pack

# Ignore environment files
.env
.env.local
.env.development
.env.test
.env.production

# Ignore build outputs
/build/
/dist/
/.next/

# Ignore cache files
.cache/
*.cache
.eslintcache
.npm

# Ignore logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Ignore .vscode folder if it's present
.vscode/

# Ignore system files (e.g. macOS, Windows)
.DS_Store
Thumbs.db
desktop.ini

# Ignore test coverage
/coverage/

/data/db/
