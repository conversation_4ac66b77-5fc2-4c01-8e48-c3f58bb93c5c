{"name": "e-learning-platform", "version": "1.0.0", "description": "E-Learning Platform React Application", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "bootstrap": "^5.3.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mongoose": "^8.14.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-bootstrap": "^2.7.4", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0", "react-transition-group": "^4.4.5"}, "devDependencies": {"concurrently": "^9.1.2", "react-scripts": "5.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node server/server.js", "dev": "concurrently \"npm run server\" \"npm run start\""}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}