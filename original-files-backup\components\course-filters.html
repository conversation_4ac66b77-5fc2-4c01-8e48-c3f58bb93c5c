<!-- Course Filters Sidebar -->
<div class="filters sticky-lg-top" style="top: 100px;">
    <h4 class="fw-bold mb-4">Filters</h4>
    
    <!-- Categories Filter -->
    <div class="mb-4">
        <h5 class="mb-3">Categories</h5>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="categoryAll" checked>
            <label class="form-check-label" for="categoryAll">
                All Categories
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="categoryDataScience">
            <label class="form-check-label" for="categoryDataScience">
                Data Science
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="categoryWebDev">
            <label class="form-check-label" for="categoryWebDev">
                Web Development
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="categoryBusiness">
            <label class="form-check-label" for="categoryBusiness">
                Business
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="categoryDesign">
            <label class="form-check-label" for="categoryDesign">
                Design
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="categoryMarketing">
            <label class="form-check-label" for="categoryMarketing">
                Marketing
            </label>
        </div>
    </div>
    
    <!-- Level Filter -->
    <div class="mb-4">
        <h5 class="mb-3">Level</h5>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="levelAll" checked>
            <label class="form-check-label" for="levelAll">
                All Levels
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="levelBeginner">
            <label class="form-check-label" for="levelBeginner">
                Beginner
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="levelIntermediate">
            <label class="form-check-label" for="levelIntermediate">
                Intermediate
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="levelAdvanced">
            <label class="form-check-label" for="levelAdvanced">
                Advanced
            </label>
        </div>
    </div>
    
    <!-- Rating Filter -->
    <div class="mb-4">
        <h5 class="mb-3">Rating</h5>
        <div class="form-check mb-2">
            <input class="form-check-input" type="radio" name="ratingFilter" id="ratingAll" checked>
            <label class="form-check-label" for="ratingAll">
                All Ratings
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="radio" name="ratingFilter" id="rating4Plus">
            <label class="form-check-label" for="rating4Plus">
                <div>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="far fa-star text-warning"></i>
                    <span class="ms-1">& up</span>
                </div>
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="radio" name="ratingFilter" id="rating3Plus">
            <label class="form-check-label" for="rating3Plus">
                <div>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="far fa-star text-warning"></i>
                    <i class="far fa-star text-warning"></i>
                    <span class="ms-1">& up</span>
                </div>
            </label>
        </div>
    </div>
    
    <!-- Duration Filter -->
    <div class="mb-4">
        <h5 class="mb-3">Duration</h5>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="durationAll" checked>
            <label class="form-check-label" for="durationAll">
                All Durations
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="duration0to3">
            <label class="form-check-label" for="duration0to3">
                0-3 Hours
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="duration3to6">
            <label class="form-check-label" for="duration3to6">
                3-6 Hours
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="duration6to12">
            <label class="form-check-label" for="duration6to12">
                6-12 Hours
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" value="" id="duration12Plus">
            <label class="form-check-label" for="duration12Plus">
                12+ Hours
            </label>
        </div>
    </div>
    
    <!-- Price Filter -->
    <div class="mb-4">
        <h5 class="mb-3">Price</h5>
        <div class="form-check mb-2">
            <input class="form-check-input" type="radio" name="priceFilter" id="priceAll" checked>
            <label class="form-check-label" for="priceAll">
                All Prices
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="radio" name="priceFilter" id="priceFree">
            <label class="form-check-label" for="priceFree">
                Free
            </label>
        </div>
        <div class="form-check mb-2">
            <input class="form-check-input" type="radio" name="priceFilter" id="pricePaid">
            <label class="form-check-label" for="pricePaid">
                Paid
            </label>
        </div>
    </div>
    
    <button class="btn btn-primary w-100">Apply Filters</button>
    <button class="btn btn-outline-secondary w-100 mt-2">Reset Filters</button>
</div>