/* 
==============================================
LearnHub - E-Learning Platform Styles
==============================================
*/

/* General Styles */
:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #4cc9f0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --dark-color: #212529;
    --light-color: #f8f9fa;
    --gray-color: #6c757d;
    --body-font: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
    --heading-font: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
}

body {
    font-family: var(--body-font);
    color: var(--dark-color);
    line-height: 1.6;
    overflow-x: hidden;
    padding-top: 56px; /* For fixed navbar */
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    font-weight: 700;
    line-height: 1.2;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

/* Button Styles */
.btn {
    padding: 0.5rem 1.5rem;
    border-radius: 0.25rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

/* Navbar Styles */
.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.nav-link.active {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    padding: 5rem 0 3rem;
    position: relative;
    background-color: var(--light-color);
}

/* Course Card Styles */
.course-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.course-card .card-img-top {
    height: 200px;
    object-fit: cover;
}

/* Testimonial Cards */
.testimonial-card {
    border-radius: 0.5rem;
    overflow: hidden;
}

/* Footer Styles */
footer {
    background-color: var(--dark-color);
}

footer a:hover {
    color: var(--primary-color) !important;
}

/* Progress Bars for Course Completion */
.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
}

/* Course Details Page */
.course-sidebar {
    background-color: var(--light-color);
    border-radius: 0.5rem;
    padding: 1.5rem;
    position: sticky;
    top: 6rem;
}

.course-content {
    margin-top: 2rem;
}

.course-content .accordion-button:not(.collapsed) {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

/* Video Player */
.video-container {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    overflow: hidden;
    max-width: 100%;
}

.video-container iframe,
.video-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* User Dashboard */
.dashboard-sidebar {
    background-color: var(--light-color);
    border-radius: 0.5rem;
    padding: 1.5rem;
}

.dashboard-sidebar .nav-link {
    color: var(--dark-color);
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
}

.dashboard-sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white !important;
}

.dashboard-sidebar .nav-link:hover:not(.active) {
    background-color: rgba(67, 97, 238, 0.1);
}

/* Course Card in Dashboard */
.dashboard-course-card {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    overflow: hidden;
}

/* Achievements and Badges */
.badge-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: 0.5rem;
    background-color: var(--light-color);
    margin-bottom: 1.5rem;
}

.badge-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    font-size: 2rem;
}

.badge-locked .badge-icon {
    background-color: var(--gray-color);
}

.badge-locked {
    opacity: 0.7;
}

/* Quiz Styles */
.quiz-container {
    background-color: var(--light-color);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin: 2rem 0;
}

.quiz-option {
    padding: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quiz-option:hover {
    background-color: rgba(67, 97, 238, 0.05);
}

.quiz-option.selected {
    background-color: rgba(67, 97, 238, 0.1);
    border-color: var(--primary-color);
}

.quiz-option.correct {
    background-color: rgba(76, 175, 80, 0.1);
    border-color: var(--success-color);
}

.quiz-option.incorrect {
    background-color: rgba(244, 67, 54, 0.1);
    border-color: var(--danger-color);
}

/* Responsive Adjustments */
@media (max-width: 991.98px) {
    .course-sidebar {
        position: static;
        margin-bottom: 2rem;
    }
    
    .dashboard-sidebar {
        margin-bottom: 2rem;
    }
}

@media (max-width: 767.98px) {
    .hero-section {
        padding: 4rem 0 2rem;
    }
    
    .course-card .card-img-top {
        height: 180px;
    }
}

@media (max-width: 575.98px) {
    .hero-section {
        text-align: center;
    }
    
    .hero-section .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .hero-section .d-flex {
        justify-content: center;
    }
}

/* Animation Effects */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fadeIn {
    animation: fadeIn 0.8s ease forwards;
}

/* Admin Panel Styles */
.admin-sidebar {
    background-color: var(--dark-color);
    color: white;
    min-height: 100vh;
}

.admin-sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
}

.admin-sidebar .nav-link.active {
    color: white;
    background-color: var(--primary-color);
}

.admin-sidebar .nav-link:hover:not(.active) {
    color: white;
    background-color: rgba(67, 97, 238, 0.2);
}

/* File Upload */
.file-upload {
    border: 2px dashed rgba(0, 0, 0, 0.1);
    padding: 2rem;
    text-align: center;
    border-radius: 0.5rem;
    cursor: pointer;
}

.file-upload:hover {
    border-color: var(--primary-color);
}

/* Rich Text Editor */
.ck-editor__editable {
    min-height: 200px;
}